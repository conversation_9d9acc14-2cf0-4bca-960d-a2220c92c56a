#!/bin/bash

# Git Worktree 配置文件
# 定义常用分支的别名和快捷方式

# 分支别名映射
declare -A BRANCH_ALIASES=(
    ["m"]="master"
    ["main"]="master"
    ["d"]="dev-1.1.0"
    ["dev"]="dev-1.1.0"
    ["std"]="dev-1.1.0-std"
    ["pre"]="pre-sale"
    ["sale"]="pre-sale"
    ["u"]="unitree-debug"
    ["unitree"]="unitree-debug"
    ["x"]="xuhui"
    ["xu"]="xuhui-unitree"
    ["t"]="test"
    ["debug"]="debug"
)

# 解析分支别名
resolve_branch_alias() {
    local input="$1"
    if [[ -n "${BRANCH_ALIASES[$input]}" ]]; then
        echo "${BRANCH_ALIASES[$input]}"
    else
        echo "$input"
    fi
}

# 显示所有别名
show_aliases() {
    echo "配置的分支别名:"
    for alias in "${!BRANCH_ALIASES[@]}"; do
        printf "  %-10s -> %s\n" "$alias" "${BRANCH_ALIASES[$alias]}"
    done
}

# 注意：函数在被 source 时会自动可用，无需显式导出
