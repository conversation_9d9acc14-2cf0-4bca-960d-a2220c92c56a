#!/bin/bash

# GitHub仓库创建和上传脚本
# 用于将robot-application-origin项目上传到GitHub私有仓库

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# 默认配置
DEFAULT_REPO_NAME="robot-application-origin"
DEFAULT_DESCRIPTION="ROS2 Robot Application with Network Node Debug System and Multi-level Logging Configuration"
DEFAULT_BRANCH="test"

echo -e "${BLUE}🚀 GitHub仓库创建和上传脚本${NC}"
echo -e "${BLUE}================================${NC}"

# 检查是否在正确的目录
if [ ! -f ".gitignore" ] || [ ! -d "xiaoli_application_ros2" ]; then
    echo -e "${RED}❌ 错误：请在robot-application-origin根目录下运行此脚本${NC}"
    exit 1
fi

# 检查git状态
if [ -n "$(git status --porcelain)" ]; then
    echo -e "${RED}❌ 错误：工作区不干净，请先提交所有更改${NC}"
    git status
    exit 1
fi

# 获取用户输入
echo -e "${YELLOW}📝 请提供以下信息：${NC}"

read -p "GitHub用户名: " GITHUB_USERNAME
if [ -z "$GITHUB_USERNAME" ]; then
    echo -e "${RED}❌ GitHub用户名不能为空${NC}"
    exit 1
fi

read -p "仓库名称 (默认: $DEFAULT_REPO_NAME): " REPO_NAME
REPO_NAME=${REPO_NAME:-$DEFAULT_REPO_NAME}

read -p "仓库描述 (默认: $DEFAULT_DESCRIPTION): " REPO_DESCRIPTION
REPO_DESCRIPTION=${REPO_DESCRIPTION:-$DEFAULT_DESCRIPTION}

echo -e "${YELLOW}选择连接方式：${NC}"
echo "1) HTTPS (推荐，适用于大多数用户)"
echo "2) SSH (需要已配置SSH密钥)"
read -p "请选择 (1/2，默认1): " CONNECTION_TYPE
CONNECTION_TYPE=${CONNECTION_TYPE:-1}

if [ "$CONNECTION_TYPE" = "1" ]; then
    REPO_URL="https://github.com/$GITHUB_USERNAME/$REPO_NAME.git"
    CONNECTION_METHOD="HTTPS"
elif [ "$CONNECTION_TYPE" = "2" ]; then
    REPO_URL="**************:$GITHUB_USERNAME/$REPO_NAME.git"
    CONNECTION_METHOD="SSH"
else
    echo -e "${RED}❌ 无效选择${NC}"
    exit 1
fi

echo -e "${YELLOW}是否创建私有仓库？${NC}"
read -p "私有仓库 (y/N，默认N): " PRIVATE_REPO
if [[ "$PRIVATE_REPO" =~ ^[Yy]$ ]]; then
    VISIBILITY="private"
    VISIBILITY_TEXT="私有"
else
    VISIBILITY="public"
    VISIBILITY_TEXT="公开"
fi

# 确认信息
echo -e "${BLUE}📋 配置确认：${NC}"
echo -e "GitHub用户名: ${GREEN}$GITHUB_USERNAME${NC}"
echo -e "仓库名称: ${GREEN}$REPO_NAME${NC}"
echo -e "仓库描述: ${GREEN}$REPO_DESCRIPTION${NC}"
echo -e "连接方式: ${GREEN}$CONNECTION_METHOD${NC}"
echo -e "仓库URL: ${GREEN}$REPO_URL${NC}"
echo -e "可见性: ${GREEN}$VISIBILITY_TEXT${NC}"
echo -e "当前分支: ${GREEN}$DEFAULT_BRANCH${NC}"

read -p "确认创建并上传？(y/N): " CONFIRM
if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}操作已取消${NC}"
    exit 0
fi

echo -e "${BLUE}🔧 开始执行...${NC}"

# 检查GitHub CLI
if command -v gh &> /dev/null; then
    echo -e "${GREEN}✅ 检测到GitHub CLI，使用gh命令创建仓库${NC}"
    USE_GH_CLI=true
else
    echo -e "${YELLOW}⚠️  未检测到GitHub CLI，将提供手动创建指引${NC}"
    USE_GH_CLI=false
fi

# 创建GitHub仓库
if [ "$USE_GH_CLI" = true ]; then
    echo -e "${BLUE}📦 使用GitHub CLI创建仓库...${NC}"
    
    # 检查是否已登录
    if ! gh auth status &> /dev/null; then
        echo -e "${YELLOW}🔐 需要登录GitHub CLI${NC}"
        gh auth login
    fi
    
    # 创建仓库
    if [ "$VISIBILITY" = "private" ]; then
        gh repo create "$REPO_NAME" --description "$REPO_DESCRIPTION" --private
    else
        gh repo create "$REPO_NAME" --description "$REPO_DESCRIPTION" --public
    fi
    
    echo -e "${GREEN}✅ GitHub仓库创建成功${NC}"
else
    echo -e "${YELLOW}📋 请手动创建GitHub仓库：${NC}"
    echo -e "1. 访问: ${BLUE}https://github.com/new${NC}"
    echo -e "2. 仓库名称: ${GREEN}$REPO_NAME${NC}"
    echo -e "3. 描述: ${GREEN}$REPO_DESCRIPTION${NC}"
    echo -e "4. 可见性: ${GREEN}$VISIBILITY_TEXT${NC}"
    echo -e "5. ${RED}不要${NC}勾选 'Add a README file'、'Add .gitignore'、'Choose a license'"
    echo ""
    read -p "创建完成后按回车继续..."
fi

# 显示当前远程仓库
echo -e "${BLUE}📋 当前远程仓库：${NC}"
git remote -v

# 添加GitHub远程仓库（不影响现有远程仓库）
echo -e "${BLUE}🔗 添加GitHub远程仓库...${NC}"

# 检查是否已存在github远程仓库
if git remote get-url github &> /dev/null; then
    echo -e "${YELLOW}⚠️  github远程仓库已存在，将更新URL${NC}"
    git remote set-url github "$REPO_URL"
else
    echo -e "${GREEN}✅ 添加新的GitHub远程仓库（保持现有远程仓库不变）${NC}"
    git remote add github "$REPO_URL"
fi

echo -e "${GREEN}✅ GitHub远程仓库配置完成${NC}"

# 推送代码
echo -e "${BLUE}📤 推送代码到GitHub...${NC}"

# 推送当前分支
echo -e "${BLUE}推送分支: $DEFAULT_BRANCH${NC}"
git push -u github "$DEFAULT_BRANCH"

# 推送其他本地分支（如果有其他分支）
OTHER_BRANCHES=$(git branch --format='%(refname:short)' | grep -v "$DEFAULT_BRANCH" || true)
if [ -n "$OTHER_BRANCHES" ]; then
    echo -e "${BLUE}推送其他本地分支...${NC}"
    for branch in $OTHER_BRANCHES; do
        echo -e "推送分支: $branch"
        git push github "$branch" || echo -e "${YELLOW}⚠️  分支 $branch 推送失败，跳过${NC}"
    done
else
    echo -e "${YELLOW}ℹ️  只有当前分支 $DEFAULT_BRANCH，无其他分支需要推送${NC}"
fi

# 推送标签（如果有）
TAGS=$(git tag)
if [ -n "$TAGS" ]; then
    echo -e "${BLUE}📋 推送标签...${NC}"
    git push github --tags
fi

echo -e "${GREEN}✅ 代码推送完成${NC}"

# 显示结果
echo -e "${BLUE}🎉 GitHub仓库创建和上传完成！${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "仓库地址: ${GREEN}https://github.com/$GITHUB_USERNAME/$REPO_NAME${NC}"
echo -e "克隆命令: ${GREEN}git clone $REPO_URL${NC}"
echo -e "新增远程仓库: ${GREEN}github -> $REPO_URL${NC}"

# 显示所有远程仓库列表
echo -e "${BLUE}📋 所有远程仓库（现有仓库保持不变）：${NC}"
git remote -v

echo -e "${GREEN}✅ 现有远程仓库（origin、gitlab-remote）保持不变${NC}"
echo -e "${GREEN}✅ 新增GitHub远程仓库：github${NC}"
echo -e "${GREEN}🚀 现在你可以在GitHub上查看你的仓库了！${NC}"
