#!/bin/bash

# 快速GitHub仓库设置脚本
# 适用于已经手动创建了GitHub仓库的情况

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}⚡ 快速GitHub仓库设置脚本${NC}"
echo -e "${BLUE}========================${NC}"

# 检查是否在正确的目录
if [ ! -f ".gitignore" ] || [ ! -d "xiaoli_application_ros2" ]; then
    echo -e "${RED}❌ 错误：请在robot-application-origin根目录下运行此脚本${NC}"
    exit 1
fi

# 检查git状态
if [ -n "$(git status --porcelain)" ]; then
    echo -e "${RED}❌ 错误：工作区不干净，请先提交所有更改${NC}"
    git status
    exit 1
fi

# 获取用户输入
read -p "GitHub用户名: " GITHUB_USERNAME
if [ -z "$GITHUB_USERNAME" ]; then
    echo -e "${RED}❌ GitHub用户名不能为空${NC}"
    exit 1
fi

read -p "仓库名称: " REPO_NAME
if [ -z "$REPO_NAME" ]; then
    echo -e "${RED}❌ 仓库名称不能为空${NC}"
    exit 1
fi

echo -e "${YELLOW}选择连接方式：${NC}"
echo "1) HTTPS (推荐)"
echo "2) SSH"
read -p "请选择 (1/2，默认1): " CONNECTION_TYPE
CONNECTION_TYPE=${CONNECTION_TYPE:-1}

if [ "$CONNECTION_TYPE" = "1" ]; then
    REPO_URL="https://github.com/$GITHUB_USERNAME/$REPO_NAME.git"
elif [ "$CONNECTION_TYPE" = "2" ]; then
    REPO_URL="**************:$GITHUB_USERNAME/$REPO_NAME.git"
else
    echo -e "${RED}❌ 无效选择${NC}"
    exit 1
fi

echo -e "${BLUE}📋 配置信息：${NC}"
echo -e "GitHub用户名: ${GREEN}$GITHUB_USERNAME${NC}"
echo -e "仓库名称: ${GREEN}$REPO_NAME${NC}"
echo -e "仓库URL: ${GREEN}$REPO_URL${NC}"

read -p "确认设置？(y/N): " CONFIRM
if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}操作已取消${NC}"
    exit 0
fi

# 显示当前远程仓库
echo -e "${BLUE}📋 当前远程仓库：${NC}"
git remote -v

# 添加GitHub远程仓库（不影响现有远程仓库）
echo -e "${BLUE}🔗 添加GitHub远程仓库...${NC}"
if git remote get-url github &> /dev/null; then
    echo -e "${YELLOW}⚠️  github远程仓库已存在，将更新URL${NC}"
    git remote set-url github "$REPO_URL"
else
    echo -e "${GREEN}✅ 添加新的GitHub远程仓库（保持现有远程仓库不变）${NC}"
    git remote add github "$REPO_URL"
fi

# 推送代码
echo -e "${BLUE}📤 推送代码到GitHub...${NC}"
git push -u github test

echo -e "${GREEN}✅ 完成！${NC}"
echo -e "仓库地址: ${GREEN}https://github.com/$GITHUB_USERNAME/$REPO_NAME${NC}"

# 显示所有远程仓库
echo -e "${BLUE}📋 所有远程仓库（现有仓库保持不变）：${NC}"
git remote -v

echo -e "${GREEN}✅ 现有远程仓库保持不变，新增GitHub远程仓库${NC}"
