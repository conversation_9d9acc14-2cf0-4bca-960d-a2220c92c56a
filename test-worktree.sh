#!/bin/bash

echo "=== 测试 Git Worktree 工具 ==="
echo ""

# 测试配置文件加载
echo "1. 测试配置文件加载..."
if source worktree-config.sh 2>/dev/null; then
    echo "✅ 配置文件加载成功"
else
    echo "❌ 配置文件加载失败"
fi

# 测试别名解析
echo ""
echo "2. 测试别名解析..."
if command -v resolve_branch_alias &> /dev/null; then
    test_alias=$(resolve_branch_alias "m")
    if [[ "$test_alias" == "master" ]]; then
        echo "✅ 别名解析正常: m -> $test_alias"
    else
        echo "❌ 别名解析异常: m -> $test_alias"
    fi
else
    echo "❌ 别名解析函数不可用"
fi

# 测试主脚本加载
echo ""
echo "3. 测试主脚本加载..."
if source wt.sh 2>/dev/null; then
    echo "✅ 主脚本加载成功"
else
    echo "❌ 主脚本加载失败"
fi

# 测试函数可用性
echo ""
echo "4. 测试函数可用性..."
if command -v wt &> /dev/null; then
    echo "✅ wt 函数可用"
else
    echo "❌ wt 函数不可用"
fi

if command -v wtlist &> /dev/null; then
    echo "✅ wtlist 函数可用"
else
    echo "❌ wtlist 函数不可用"
fi

if command -v wtstatus &> /dev/null; then
    echo "✅ wtstatus 函数可用"
else
    echo "❌ wtstatus 函数不可用"
fi

echo ""
echo "=== 测试完成 ==="
