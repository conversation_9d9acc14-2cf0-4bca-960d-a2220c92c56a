#!/bin/bash

# 大文件恢复脚本
# 用途: 将之前移动的大文件恢复到原位置

# 配置参数
BACKUP_DIR="large_files_backup"  # 备份目录

# 初始化计数器
restored_count=0
failed_count=0
total_size=0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示帮助信息
show_help() {
    echo "🔄 大文件恢复脚本"
    echo "=================="
    echo ""
    echo "用途: 恢复之前通过 move_large_files.sh 移动的文件"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -l, --list     仅列出可恢复的文件，不执行恢复"
    echo "  -f, --force    强制恢复，覆盖已存在的文件"
    echo "  -d, --dir DIR  指定备份目录（默认: $BACKUP_DIR）"
    echo ""
    echo "示例:"
    echo "  $0              # 交互式恢复所有文件"
    echo "  $0 --list       # 列出可恢复的文件"
    echo "  $0 --force      # 强制恢复所有文件"
    echo ""
}

# 列出可恢复的文件
list_files() {
    if [ ! -d "$BACKUP_DIR" ]; then
        print_error "备份目录不存在: $BACKUP_DIR"
        exit 1
    fi
    
    echo "📋 可恢复的文件列表:"
    echo "==================="
    
    local file_count=0
    local total_size_mb=0
    
    find "$BACKUP_DIR" -type f | while read backup_file; do
        # 获取原始路径（移除备份目录前缀）
        original_file="${backup_file#$BACKUP_DIR/}"
        
        # 获取文件大小
        file_size=$(du -h "$backup_file" | cut -f1)
        file_size_mb=$(du -m "$backup_file" | cut -f1)
        
        echo "📄 $original_file (大小: $file_size)"
        
        # 检查原位置是否已存在文件
        if [ -f "$original_file" ]; then
            echo "   ⚠️  原位置已存在文件"
        else
            echo "   ✅ 可以安全恢复"
        fi
        
        ((file_count++))
        ((total_size_mb += file_size_mb))
    done
    
    echo ""
    echo "📊 统计信息:"
    echo "   - 文件数量: $file_count"
    echo "   - 总大小: ${total_size_mb}MB"
}

# 恢复单个文件
restore_file() {
    local backup_file="$1"
    local force_restore="$2"
    
    # 获取原始路径
    local original_file="${backup_file#$BACKUP_DIR/}"
    local original_dir=$(dirname "$original_file")
    
    # 获取文件大小
    local file_size=$(du -h "$backup_file" | cut -f1)
    
    echo "📄 恢复文件: $original_file (大小: $file_size)"
    
    # 检查原位置是否已存在文件
    if [ -f "$original_file" ] && [ "$force_restore" != "true" ]; then
        echo "⚠️  原位置已存在文件: $original_file"
        read -p "是否覆盖? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "⏭️  跳过: $original_file"
            return 1
        fi
    fi
    
    # 创建目标目录
    if [ "$original_dir" != "." ]; then
        mkdir -p "$original_dir"
    fi
    
    # 恢复文件
    if mv "$backup_file" "$original_file"; then
        echo "✅ 成功恢复: $original_file"
        ((restored_count++))
        
        # 计算总大小
        local size_mb=$(du -m "$original_file" | cut -f1)
        ((total_size += size_mb))
        
        return 0
    else
        echo "❌ 恢复失败: $original_file"
        ((failed_count++))
        return 1
    fi
}

# 主恢复函数
restore_files() {
    local force_restore="$1"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        print_error "备份目录不存在: $BACKUP_DIR"
        print_info "请确保之前运行过 move_large_files.sh 脚本"
        exit 1
    fi
    
    echo "🔄 开始恢复大文件..."
    echo "📁 备份目录: $BACKUP_DIR"
    if [ "$force_restore" = "true" ]; then
        echo "⚡ 强制模式: 将覆盖已存在的文件"
    fi
    echo "=================================="
    
    # 查找所有备份文件
    find "$BACKUP_DIR" -type f | while read backup_file; do
        restore_file "$backup_file" "$force_restore"
        echo "---"
    done
    
    echo ""
    echo "🎉 文件恢复完成！"
    echo "=================================="
    echo "📊 统计信息:"
    echo "   - 成功恢复: ${restored_count:-0} 个文件"
    echo "   - 恢复失败: ${failed_count:-0} 个文件"
    echo "   - 总大小: ${total_size:-0}MB"
    
    # 检查备份目录是否为空
    if [ -z "$(find "$BACKUP_DIR" -type f)" ]; then
        echo ""
        echo "📁 备份目录已空，是否删除备份目录?"
        read -p "删除备份目录 $BACKUP_DIR? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$BACKUP_DIR"
            echo "✅ 备份目录已删除"
        fi
    fi
}

# 主函数
main() {
    local list_only=false
    local force_restore=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -l|--list)
                list_only=true
                shift
                ;;
            -f|--force)
                force_restore=true
                shift
                ;;
            -d|--dir)
                BACKUP_DIR="$2"
                shift 2
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行相应操作
    if [ "$list_only" = true ]; then
        list_files
    else
        restore_files "$force_restore"
    fi
}

# 运行主函数
main "$@"
