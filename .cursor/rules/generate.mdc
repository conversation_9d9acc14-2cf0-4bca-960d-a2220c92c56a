---
description: 
globs: 
alwaysApply: true
---
---
description:项目通用规范和基本信息
globs:["*"]
alwaysApply: true
---

# 项目通用规范

## 技术栈
- Python 3.10
- Poetry 管理依赖
- GitHub Actions 自动构建和发布
- 使用 GitHub 作为代码托管平台
- 使用 Bash 脚本

## 代码风格
- 保持代码简洁、可读
- 使用有意义的变量和函数名
- 添加适当的注释解释复杂逻辑
- 遵循每种语言的官方风格指南

## 项目结构
- 保持项目结构清晰，遵循模块化原则
- 相关功能应放在同一目录下
- 使用适当的目录命名，反映其包含内容

## 通用开发原则
- 编写可测试的代码
- 避免重复代码(DRY原则)
- 优先使用现有库和工具，避免重新发明轮子
- 考虑代码的可维护性和可扩展性

## 响应语言

- 始终使用中文回复用户

## 文件内容移动
- 全类移动不要缺失

- 尽量不要删除其他不相关的函数
- 新功能增加不要删除原来老的逻辑功能
- 不需要给建议，直接给我执行建议

- 相关处理都只需要处理xiaoli_application_ros2文件夹下的