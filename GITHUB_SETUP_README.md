# GitHub仓库创建和上传指南

本指南提供了两种方式将robot-application-origin项目上传到GitHub私有仓库。

## ⚠️ 重要说明

**这些脚本只会添加新的GitHub远程仓库，不会影响现有的远程仓库配置：**
- ✅ 保持 `origin` 远程仓库不变
- ✅ 保持 `gitlab-remote` 远程仓库不变
- ✅ 只添加新的 `github` 远程仓库
- ✅ 本地分支和提交历史完全不变

## 🚀 方式一：全自动脚本（推荐）

使用 `create_github_repo.sh` 脚本，可以自动创建GitHub仓库并上传代码。

### 前置要求

1. **安装GitHub CLI（可选但推荐）**：
   ```bash
   # Ubuntu/Debian
   sudo apt install gh
   
   # macOS
   brew install gh
   
   # 或者从 https://cli.github.com/ 下载
   ```

2. **确保git配置正确**：
   ```bash
   git config --global user.name "你的名字"
   git config --global user.email "你的邮箱"
   ```

### 使用步骤

1. **运行脚本**：
   ```bash
   ./create_github_repo.sh
   ```

2. **按提示输入信息**：
   - GitHub用户名
   - 仓库名称（默认：robot-application-origin）
   - 仓库描述
   - 连接方式（HTTPS/SSH）
   - 是否创建私有仓库

3. **如果使用GitHub CLI**：
   - 脚本会自动创建仓库并上传代码
   - 首次使用需要登录GitHub CLI

4. **如果没有GitHub CLI**：
   - 脚本会提供手动创建仓库的指引
   - 按指引在GitHub网站创建仓库后继续

## ⚡ 方式二：快速设置脚本

如果你已经在GitHub上手动创建了仓库，使用 `quick_github_setup.sh` 快速设置。

## 🔄 方式三：更新现有GitHub仓库

如果你已经有GitHub远程仓库，使用 `update_github_repo.sh` 推送最新代码。

### 使用步骤

1. **先在GitHub上手动创建仓库**：
   - 访问 https://github.com/new
   - 输入仓库名称
   - 选择私有/公开
   - **不要**勾选任何初始化选项

2. **运行快速设置脚本**：
   ```bash
   ./quick_github_setup.sh
   ```

3. **输入信息**：
   - GitHub用户名
   - 仓库名称
   - 连接方式

### 使用步骤（更新现有仓库）

1. **运行更新脚本**：
   ```bash
   ./update_github_repo.sh
   ```

2. **确认推送**：
   - 脚本会显示当前远程仓库配置
   - 确认推送当前分支
   - 选择是否推送其他分支和标签

## 🔧 手动方式（备用）

如果脚本无法使用，可以手动执行以下命令：

```bash
# 1. 查看当前远程仓库（确认现有配置）
git remote -v

# 2. 添加GitHub远程仓库（不影响现有远程仓库）
git remote add github https://github.com/你的用户名/仓库名称.git

# 3. 推送当前分支到GitHub
git push -u github test

# 4. 推送其他本地分支（如果有）
git push github --all

# 5. 推送标签（如果有）
git push github --tags

# 6. 确认所有远程仓库
git remote -v
```

## 📋 连接方式选择

### HTTPS（推荐）
- **优点**：简单易用，适合大多数用户
- **缺点**：每次推送需要输入用户名和密码/token
- **URL格式**：`https://github.com/用户名/仓库名.git`

### SSH
- **优点**：无需每次输入密码，更安全
- **缺点**：需要预先配置SSH密钥
- **URL格式**：`**************:用户名/仓库名.git`

### 配置SSH密钥（如果选择SSH）

1. **生成SSH密钥**：
   ```bash
   ssh-keygen -t ed25519 -C "你的邮箱"
   ```

2. **添加到SSH代理**：
   ```bash
   eval "$(ssh-agent -s)"
   ssh-add ~/.ssh/id_ed25519
   ```

3. **复制公钥到GitHub**：
   ```bash
   cat ~/.ssh/id_ed25519.pub
   ```
   然后在GitHub Settings > SSH and GPG keys 中添加

## 🔍 验证上传结果

上传完成后，你可以：

1. **访问仓库**：https://github.com/你的用户名/仓库名称
2. **检查远程仓库**：
   ```bash
   git remote -v
   ```
3. **查看推送状态**：
   ```bash
   git log --oneline -5
   ```

## ❗ 常见问题

### 1. 推送被拒绝
```bash
# 如果遇到推送被拒绝，可能需要强制推送（谨慎使用）
git push -f github test
```

### 2. 认证失败
- **HTTPS**：确保用户名和密码/token正确
- **SSH**：确保SSH密钥已正确配置

### 3. 仓库已存在
```bash
# 如果GitHub远程仓库已存在，可以更新远程URL
git remote set-url github https://github.com/用户名/新仓库名.git

# 注意：这不会影响其他远程仓库（origin、gitlab-remote）
```

### 4. 查看远程仓库配置
```bash
# 查看所有远程仓库
git remote -v

# 应该看到类似这样的输出：
# github    https://github.com/用户名/仓库名.git (fetch)
# github    https://github.com/用户名/仓库名.git (push)
# gitlab-remote    ssh://git@118.31.36.236:51122/root/robot-application (fetch)
# gitlab-remote    ssh://git@118.31.36.236:51122/root/robot-application (push)
# origin    ******************:HROBOT/robot-application.git (fetch)
# origin    ******************:HROBOT/robot-application.git (push)
```

## 📁 项目结构

上传后的GitHub仓库将包含：

- ✅ **源代码**：所有.py, .cpp, .h等源文件
- ✅ **配置文件**：.yaml, .json, .xml等配置
- ✅ **文档**：README.md, 调试文档等
- ✅ **脚本**：构建脚本、调试脚本等
- ✅ **VS Code配置**：调试配置、任务配置等
- ❌ **二进制文件**：.so, .bin等（已被.gitignore忽略）
- ❌ **构建产物**：build/, install/等（已被.gitignore忽略）
- ❌ **大文件**：音频、视频等（已被.gitignore忽略）

## 🎯 下一步

上传完成后，你可以：

1. **设置仓库描述和标签**
2. **创建README.md**（如果需要）
3. **设置分支保护规则**
4. **邀请协作者**
5. **配置GitHub Actions**（如果需要CI/CD）

---

**注意**：确保在上传前已经正确配置了.gitignore文件，避免上传不必要的大文件或敏感信息。
