#!/bin/bash

# 只上传最新commit到GitHub脚本
# 避免上传历史记录中的大文件，创建一个全新的提交历史

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}📤 只上传最新commit到GitHub脚本${NC}"
echo -e "${BLUE}===============================${NC}"

# 检查是否在正确的目录
if [ ! -f ".gitignore" ] || [ ! -d "xiaoli_application_ros2" ]; then
    echo -e "${RED}❌ 错误：请在robot-application-origin根目录下运行此脚本${NC}"
    exit 1
fi

# 检查git状态
if [ -n "$(git status --porcelain)" ]; then
    echo -e "${RED}❌ 错误：工作区不干净，请先提交所有更改${NC}"
    git status
    exit 1
fi

# 显示当前远程仓库
echo -e "${BLUE}📋 当前远程仓库配置：${NC}"
git remote -v

# 检查是否存在github远程仓库
if ! git remote get-url github &> /dev/null; then
    echo -e "${RED}❌ 错误：未找到github远程仓库${NC}"
    echo -e "${YELLOW}请先运行 ./create_github_repo.sh 或 ./quick_github_setup.sh${NC}"
    exit 1
fi

GITHUB_URL=$(git remote get-url github)
echo -e "${GREEN}✅ 找到GitHub远程仓库: $GITHUB_URL${NC}"

# 获取当前分支和commit信息
CURRENT_BRANCH=$(git branch --show-current)
CURRENT_COMMIT=$(git rev-parse HEAD)
COMMIT_MESSAGE=$(git log -1 --pretty=format:"%s")

echo -e "${BLUE}📍 当前分支: $CURRENT_BRANCH${NC}"
echo -e "${BLUE}📍 当前commit: ${CURRENT_COMMIT:0:8}${NC}"
echo -e "${BLUE}📍 提交信息: $COMMIT_MESSAGE${NC}"

# 警告信息
echo -e "${YELLOW}⚠️  警告：此操作将创建一个全新的提交历史${NC}"
echo -e "${YELLOW}⚠️  GitHub上的历史记录将被完全替换${NC}"
echo -e "${YELLOW}⚠️  这将避免上传历史记录中的大文件${NC}"

read -p "确认只上传最新commit到GitHub？(y/N): " CONFIRM
if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}操作已取消${NC}"
    exit 0
fi

# 创建临时分支用于上传
TEMP_BRANCH="temp-upload-$(date +%s)"
echo -e "${BLUE}🔧 创建临时分支: $TEMP_BRANCH${NC}"

# 创建孤立分支（没有历史记录）
git checkout --orphan "$TEMP_BRANCH"

# 添加所有当前文件
echo -e "${BLUE}📦 添加当前所有文件...${NC}"
git add .

# 创建新的初始提交
echo -e "${BLUE}💾 创建新的初始提交...${NC}"
git commit -m "Initial commit: $COMMIT_MESSAGE

原始commit: $CURRENT_COMMIT
上传时间: $(date)
说明: 只包含最新代码，不包含历史记录以避免大文件问题"

# 强制推送到GitHub（替换所有历史）
echo -e "${BLUE}📤 强制推送到GitHub（替换历史记录）...${NC}"
git push -f github "$TEMP_BRANCH:$CURRENT_BRANCH"

# 切换回原分支
echo -e "${BLUE}🔄 切换回原分支...${NC}"
git checkout "$CURRENT_BRANCH"

# 删除临时分支
echo -e "${BLUE}🗑️  删除临时分支...${NC}"
git branch -D "$TEMP_BRANCH"

# 显示结果
echo -e "${GREEN}✅ 最新commit上传完成！${NC}"
echo -e "${BLUE}================================${NC}"

# 获取GitHub仓库信息
if [[ "$GITHUB_URL" =~ github\.com[:/]([^/]+)/([^/]+)(\.git)?$ ]]; then
    GITHUB_USER="${BASH_REMATCH[1]}"
    GITHUB_REPO="${BASH_REMATCH[2]}"
    echo -e "GitHub仓库: ${GREEN}https://github.com/$GITHUB_USER/$GITHUB_REPO${NC}"
fi

echo -e "${GREEN}✅ GitHub仓库现在只包含最新的代码${NC}"
echo -e "${GREEN}✅ 历史记录中的大文件已被移除${NC}"
echo -e "${GREEN}✅ 本地仓库和其他远程仓库保持不变${NC}"

echo -e "${BLUE}📋 所有远程仓库：${NC}"
git remote -v

echo -e "${YELLOW}💡 提示：如果需要恢复GitHub上的历史记录，可以重新推送完整历史${NC}"
