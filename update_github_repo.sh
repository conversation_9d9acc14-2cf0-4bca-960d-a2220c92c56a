#!/bin/bash

# 更新现有GitHub仓库脚本
# 用于推送最新代码到已存在的GitHub仓库，不影响其他远程仓库

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}📤 更新GitHub仓库脚本${NC}"
echo -e "${BLUE}==================${NC}"

# 检查是否在正确的目录
if [ ! -f ".gitignore" ] || [ ! -d "xiaoli_application_ros2" ]; then
    echo -e "${RED}❌ 错误：请在robot-application-origin根目录下运行此脚本${NC}"
    exit 1
fi

# 检查git状态
if [ -n "$(git status --porcelain)" ]; then
    echo -e "${RED}❌ 错误：工作区不干净，请先提交所有更改${NC}"
    git status
    exit 1
fi

# 显示当前远程仓库
echo -e "${BLUE}📋 当前远程仓库配置：${NC}"
git remote -v

# 检查是否存在github远程仓库
if ! git remote get-url github &> /dev/null; then
    echo -e "${RED}❌ 错误：未找到github远程仓库${NC}"
    echo -e "${YELLOW}请先运行 ./create_github_repo.sh 或 ./quick_github_setup.sh${NC}"
    exit 1
fi

GITHUB_URL=$(git remote get-url github)
echo -e "${GREEN}✅ 找到GitHub远程仓库: $GITHUB_URL${NC}"

# 获取当前分支
CURRENT_BRANCH=$(git branch --show-current)
echo -e "${BLUE}📍 当前分支: $CURRENT_BRANCH${NC}"

# 确认推送
echo -e "${YELLOW}准备推送到GitHub仓库...${NC}"
read -p "确认推送当前分支 '$CURRENT_BRANCH' 到GitHub？(y/N): " CONFIRM
if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}操作已取消${NC}"
    exit 0
fi

# 推送当前分支
echo -e "${BLUE}📤 推送分支 '$CURRENT_BRANCH' 到GitHub...${NC}"
git push github "$CURRENT_BRANCH"

# 询问是否推送其他分支
OTHER_BRANCHES=$(git branch --format='%(refname:short)' | grep -v "$CURRENT_BRANCH" || true)
if [ -n "$OTHER_BRANCHES" ]; then
    echo -e "${BLUE}📋 发现其他本地分支：${NC}"
    echo "$OTHER_BRANCHES"
    read -p "是否推送所有本地分支到GitHub？(y/N): " PUSH_ALL
    if [[ "$PUSH_ALL" =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}📤 推送所有本地分支...${NC}"
        for branch in $OTHER_BRANCHES; do
            echo -e "推送分支: $branch"
            git push github "$branch" || echo -e "${YELLOW}⚠️  分支 $branch 推送失败，跳过${NC}"
        done
    fi
fi

# 询问是否推送标签
TAGS=$(git tag)
if [ -n "$TAGS" ]; then
    echo -e "${BLUE}📋 发现标签：${NC}"
    git tag | head -5
    if [ $(git tag | wc -l) -gt 5 ]; then
        echo "... 还有 $(($(git tag | wc -l) - 5)) 个标签"
    fi
    read -p "是否推送所有标签到GitHub？(y/N): " PUSH_TAGS
    if [[ "$PUSH_TAGS" =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}📋 推送标签...${NC}"
        git push github --tags
    fi
fi

# 显示结果
echo -e "${GREEN}✅ GitHub仓库更新完成！${NC}"
echo -e "${BLUE}================================${NC}"

# 获取GitHub仓库信息
if [[ "$GITHUB_URL" =~ github\.com[:/]([^/]+)/([^/]+)(\.git)?$ ]]; then
    GITHUB_USER="${BASH_REMATCH[1]}"
    GITHUB_REPO="${BASH_REMATCH[2]}"
    echo -e "GitHub仓库: ${GREEN}https://github.com/$GITHUB_USER/$GITHUB_REPO${NC}"
fi

echo -e "${BLUE}📋 所有远程仓库（其他远程仓库保持不变）：${NC}"
git remote -v

echo -e "${GREEN}✅ 现有远程仓库（origin、gitlab-remote）保持不变${NC}"
echo -e "${GREEN}✅ GitHub仓库已更新到最新代码${NC}"
