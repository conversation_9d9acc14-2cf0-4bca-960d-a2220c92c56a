wangjie update 2025-02-14:
服务部署：（注意，保持同一台机器的两块板子部署时使用相同参数，禁止一个板子部署标准版一个板子部署高配版）
1.讯飞ROS2服务部署：
运行robot-application/xiaoli_application_ros2/service/cat/install.sh脚本，参数量为0或1个
缺省/base：标准版配置，视频推流及智话功能部署于讯飞板，使用头顶摄像头；
pro：高配版配置，视频推流及智话功能部署于英伟达板，使用领结摄像头。
2.英伟达ROS2服务部署：
运行robot-application/xiaoli_application_ros2/service/nvidia/install_nvidia.sh脚本，参数量为0或1个
缺省/base：标准版配置，视频推流及智话功能部署于讯飞板，使用头顶摄像头；
pro：高配版配置，视频推流及智话功能部署于英伟达板，使用领结摄像头。

gaoyajun update 2024-12-02：
1. websocket服务模块的逻辑交互：
1.1 协议框架，基于可拓展json协议，实现客户端和服务端的数据交互，服务端作为协议层的中转层，通过解析目标客户端字段发送给指定客户端，实现客户端和客户端的通信，json格式如下：
{
    "client_type":"launcher",      //当前客户端（nvidia、launcher、deeprobot）
    "target_client":"nvidia",      //目标客户端
    "action" : "",                 //指定动作类型
    "params" : {                   //指定动作类型对应的参数
    }
}
1.2 当前应用客户端/服务端部署情况(ros2的node类型)：
1.2.1 服务端：应用在nvidia_control/homi_ws_server；
1.2.2 客户端（nvidia）：部署在英伟达主控 nvidia_control/nvidia_control_node
1.2.3 客户端（launcher）：部署在讯飞3588主控
1.2.4 客户端（deeprobot）：部署在云深处运动主控
1.2.5 客户端（android）：部署在android
server->client 协议:
{
    "client_type":"launcher",
    "target_client":"android",
    "action" : "targetPose",
    "params" : {
        "latitude": 30.274100,
        "longitude": 120.155070,
    }
}

{
    "client_type":"launcher",
    "target_client":"android",
    "action" : "robotPose",
    "params" : {
        "latitude": 30.274100,
        "longitude": 120.155070,
    }
}


client->server 协议:
{
    "client_type":"android",
    "target_client":"launcher",
    "action" : "pathPlanning",
    "params" : {

    }
}

1.2 libWebsoket调用方法（C++ 头文件为：libWebSocket.h）:
1.2.1 客户端初始化：
    WS_Init(EN_WS_ClIENT, 19002);                    //类型初始化
    WS_SetMsgCallback(notifyWsMsgCallback, this);    //设置接受msg的回调函数，json格式去解析，传参handle可以是类的句柄，方便调用类的函数
    WS_Connect("ws://*************:19002");          //客户端初始化才需要掉用（服务端不需要，参数为服务端部署的主机ip和端口）

    void notifyWsMsgCallback(void *handle, const char *msg, int index) {
        Json::Reader reader;
        Json::Value value;
        if (false == reader.parse(msg, value)) {
            return;
        }
    }
1.2.2 服务端初始化：
    WS_Init(EN_WS_SEVER, 19002);                     //类型初始化
    WS_SetMsgCallback(notifyWsMsgCallback, this);    //设置接受msg的回调函数，json格式去解析，传参handle可以是类的句柄，方便调用类的函数

    void notifyWsMsgCallback(void *handle, const char *msg, int index) {
        Json::Reader reader;
        Json::Value value;
        if (false == reader.parse(msg, value)) {
            return;
        }
    }
1.2.3 心跳：防止长时间网络等问题导致客户端异常：服务端可以定时10s发送向客户端发送keepalive，参考homi_ws_server的使用；客户端去解析更新时间戳，定时去判断是否收到服务端的心跳判断是否需要重连


#TODO
1. 部署在讯飞3588的应用：
1.1 deeprobots_application_ros1：基于ROS1的云深处机器狗应用框架
1.2 cmcc_audio_ros1：音视频应用

2. 部署在主控3588的应用：
2.1 andlink: 设备配网、蓝牙配网等功能
2.2 deeprob_ws_ctrl: 不依赖ROS框架的进程，目前有uwb数据接收模块，包含webSocket通信模块（客户端）

3. 部署在感知主控的应用：
3.1 follow_me_ui_deep_ros2： ROS2跟随框架
 