#!/bin/bash

# Git Worktree 管理脚本
# 用法: ./worktree-manager.sh [命令] [分支名] [可选参数]

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MAIN_REPO_DIR="$SCRIPT_DIR"
WORKTREE_BASE_DIR="/mine/worktrees"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Git Worktree 管理工具${NC}"
    echo ""
    echo "用法: $0 [命令] [分支名] [选项]"
    echo ""
    echo "命令:"
    echo "  add <分支名> [目录名]     - 添加新的 worktree"
    echo "  switch <分支名>          - 切换到指定分支的 worktree"
    echo "  list                     - 列出所有 worktree"
    echo "  remove <分支名>          - 删除指定的 worktree"
    echo "  clean                    - 清理无效的 worktree"
    echo "  status                   - 显示所有 worktree 状态"
    echo "  help                     - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 add master            - 为 master 分支创建 worktree"
    echo "  $0 add dev-1.1.0 dev     - 为 dev-1.1.0 分支创建名为 dev 的 worktree"
    echo "  $0 switch master         - 切换到 master 分支的 worktree"
    echo "  $0 list                  - 列出所有 worktree"
    echo "  $0 remove dev-1.1.0      - 删除 dev-1.1.0 的 worktree"
}

# 检查分支是否存在
check_branch_exists() {
    local branch_name="$1"
    if git show-ref --verify --quiet refs/heads/"$branch_name" || \
       git show-ref --verify --quiet refs/remotes/origin/"$branch_name" || \
       git show-ref --verify --quiet refs/remotes/gitlab-remote/"$branch_name"; then
        return 0
    else
        return 1
    fi
}

# 添加 worktree
add_worktree() {
    local branch_name="$1"
    local dir_name="${2:-$branch_name}"
    local worktree_path="$WORKTREE_BASE_DIR/$dir_name"
    
    if [[ -z "$branch_name" ]]; then
        echo -e "${RED}错误: 请指定分支名${NC}"
        return 1
    fi
    
    # 创建 worktree 基础目录
    mkdir -p "$WORKTREE_BASE_DIR"
    
    # 检查 worktree 是否已存在
    if [[ -d "$worktree_path" ]]; then
        echo -e "${YELLOW}警告: Worktree 目录 $worktree_path 已存在${NC}"
        return 1
    fi
    
    # 检查分支是否存在
    if ! check_branch_exists "$branch_name"; then
        echo -e "${RED}错误: 分支 '$branch_name' 不存在${NC}"
        echo "可用分支:"
        git branch -a | grep -E "(origin|gitlab-remote)/$branch_name" || echo "  未找到匹配的远程分支"
        return 1
    fi
    
    echo -e "${BLUE}正在为分支 '$branch_name' 创建 worktree...${NC}"
    
    # 尝试从本地分支创建
    if git show-ref --verify --quiet refs/heads/"$branch_name"; then
        git worktree add "$worktree_path" "$branch_name"
    # 尝试从 origin 远程分支创建
    elif git show-ref --verify --quiet refs/remotes/origin/"$branch_name"; then
        git worktree add "$worktree_path" -b "$branch_name" "origin/$branch_name"
    # 尝试从 gitlab-remote 远程分支创建
    elif git show-ref --verify --quiet refs/remotes/gitlab-remote/"$branch_name"; then
        git worktree add "$worktree_path" -b "$branch_name" "gitlab-remote/$branch_name"
    fi
    
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}成功创建 worktree: $worktree_path${NC}"
        echo -e "${BLUE}切换到新的 worktree: cd $worktree_path${NC}"
    else
        echo -e "${RED}创建 worktree 失败${NC}"
        return 1
    fi
}

# 切换到 worktree
switch_worktree() {
    local branch_name="$1"
    local worktree_path="$WORKTREE_BASE_DIR/$branch_name"
    
    if [[ -z "$branch_name" ]]; then
        echo -e "${RED}错误: 请指定分支名${NC}"
        return 1
    fi
    
    if [[ ! -d "$worktree_path" ]]; then
        echo -e "${RED}错误: Worktree '$worktree_path' 不存在${NC}"
        echo -e "${YELLOW}提示: 使用 '$0 add $branch_name' 创建 worktree${NC}"
        return 1
    fi
    
    echo -e "${GREEN}切换到 worktree: $worktree_path${NC}"
    cd "$worktree_path"
    
    # 如果在支持的终端中，更新终端标题
    if [[ -n "$TERM" ]]; then
        echo -ne "\033]0;Worktree: $branch_name\007"
    fi
    
    # 启动新的 shell 会话
    exec bash
}

# 列出所有 worktree
list_worktrees() {
    echo -e "${BLUE}所有 Git Worktrees:${NC}"
    git worktree list
    echo ""
    
    if [[ -d "$WORKTREE_BASE_DIR" ]]; then
        echo -e "${BLUE}Worktree 目录结构:${NC}"
        ls -la "$WORKTREE_BASE_DIR" 2>/dev/null || echo "  无 worktree 目录"
    fi
}

# 删除 worktree
remove_worktree() {
    local branch_name="$1"
    local worktree_path="$WORKTREE_BASE_DIR/$branch_name"
    
    if [[ -z "$branch_name" ]]; then
        echo -e "${RED}错误: 请指定分支名${NC}"
        return 1
    fi
    
    if [[ ! -d "$worktree_path" ]]; then
        echo -e "${RED}错误: Worktree '$worktree_path' 不存在${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}确认删除 worktree '$worktree_path'? (y/N)${NC}"
    read -r confirmation
    
    if [[ "$confirmation" =~ ^[Yy]$ ]]; then
        git worktree remove "$worktree_path"
        if [[ $? -eq 0 ]]; then
            echo -e "${GREEN}成功删除 worktree: $worktree_path${NC}"
        else
            echo -e "${RED}删除 worktree 失败${NC}"
        fi
    else
        echo -e "${BLUE}取消删除操作${NC}"
    fi
}

# 清理无效的 worktree
clean_worktrees() {
    echo -e "${BLUE}清理无效的 worktrees...${NC}"
    git worktree prune
    echo -e "${GREEN}清理完成${NC}"
}

# 显示所有 worktree 状态
show_status() {
    echo -e "${BLUE}=== Git Worktree 状态 ===${NC}"
    echo ""
    
    # 显示当前仓库状态
    echo -e "${YELLOW}主仓库 ($(pwd)):${NC}"
    git status --short
    echo ""
    
    # 显示所有 worktree 状态
    git worktree list | while read -r path branch commit; do
        if [[ "$path" != "$(pwd)" ]]; then
            echo -e "${YELLOW}Worktree: $path${NC}"
            if [[ -d "$path" ]]; then
                (cd "$path" && git status --short)
            else
                echo -e "${RED}  目录不存在${NC}"
            fi
            echo ""
        fi
    done
}

# 主函数
main() {
    case "$1" in
        "add")
            add_worktree "$2" "$3"
            ;;
        "switch")
            switch_worktree "$2"
            ;;
        "list")
            list_worktrees
            ;;
        "remove")
            remove_worktree "$2"
            ;;
        "clean")
            clean_worktrees
            ;;
        "status")
            show_status
            ;;
        "help"|"--help"|"-h"|"")
            show_help
            ;;
        *)
            echo -e "${RED}错误: 未知命令 '$1'${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 检查是否在 git 仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${RED}错误: 当前目录不是 Git 仓库${NC}"
    exit 1
fi

# 执行主函数
main "$@"
