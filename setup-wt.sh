#!/bin/bash

# Git Worktree 工具安装脚本
# 用法: source setup-wt.sh 或 ./setup-wt.sh

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WT_SCRIPT="$SCRIPT_DIR/wt.sh"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查 wt.sh 是否存在
if [[ ! -f "$WT_SCRIPT" ]]; then
    echo -e "${RED}错误: 找不到 wt.sh 文件${NC}"
    return 1 2>/dev/null || exit 1
fi

# 加载 wt.sh
echo -e "${BLUE}正在加载 Git Worktree 工具...${NC}"
source "$WT_SCRIPT"

# 检查是否成功加载
if command -v wt &> /dev/null; then
    echo -e "${GREEN}✓ wt 函数已加载${NC}"
else
    echo -e "${RED}✗ wt 函数加载失败${NC}"
    return 1 2>/dev/null || exit 1
fi

if command -v wtlist &> /dev/null; then
    echo -e "${GREEN}✓ wtlist 函数已加载${NC}"
else
    echo -e "${RED}✗ wtlist 函数加载失败${NC}"
fi

if command -v wtstatus &> /dev/null; then
    echo -e "${GREEN}✓ wtstatus 函数已加载${NC}"
else
    echo -e "${RED}✗ wtstatus 函数加载失败${NC}"
fi

if command -v wtremove &> /dev/null; then
    echo -e "${GREEN}✓ wtremove 函数已加载${NC}"
else
    echo -e "${RED}✗ wtremove 函数加载失败${NC}"
fi

echo ""
echo -e "${BLUE}可用命令:${NC}"
echo "  wt <分支名>       - 快速切换到指定分支"
echo "  wtlist           - 列出所有 worktrees"
echo "  wtstatus         - 显示当前状态"
echo "  wtremove <分支名> - 删除指定 worktree"
echo ""

# 自动添加到 zsh 配置文件
ZSHRC="$HOME/.zshrc"
SOURCE_LINE="source $WT_SCRIPT"

# 检查是否已经添加过
if [[ -f "$ZSHRC" ]] && grep -Fxq "$SOURCE_LINE" "$ZSHRC"; then
    echo -e "${GREEN}✓ 环境变量已存在于 ~/.zshrc 中${NC}"
else
    # 询问用户是否要自动添加
    echo -e "${YELLOW}是否要自动将 wt 工具添加到 ~/.zshrc？(y/n)${NC}"
    read -r response

    if [[ "$response" =~ ^[Yy]$ ]]; then
        # 添加注释和源码行到 .zshrc
        echo "" >> "$ZSHRC"
        echo "# Git Worktree 工具 - 自动添加于 $(date)" >> "$ZSHRC"
        echo "$SOURCE_LINE" >> "$ZSHRC"
        echo -e "${GREEN}✓ 已自动添加到 ~/.zshrc${NC}"
        echo -e "${BLUE}重新启动终端或运行 'source ~/.zshrc' 以在新会话中使用${NC}"
    else
        echo -e "${YELLOW}手动添加提示: 将以下行添加到您的 ~/.zshrc 以永久启用:${NC}"
        echo "$SOURCE_LINE"
    fi
fi

echo ""

# 创建一个快速加载别名
echo ""
echo -e "${BLUE}快速使用提示:${NC}"
echo "如果在新终端中 wt 命令不可用，请运行以下任一命令:"
echo -e "${YELLOW}  source ~/.zshrc${NC}           # 重新加载配置"
echo -e "${YELLOW}  source $WT_SCRIPT${NC}  # 直接加载 wt 工具"

# 如果脚本被直接执行（而不是被 source）
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo -e "${YELLOW}注意: 脚本被直接执行，函数只在当前会话中可用${NC}"
    echo -e "${BLUE}要在新的终端会话中使用，请运行: source $0${NC}"
fi
